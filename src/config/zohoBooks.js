const dotenv = require('dotenv');

dotenv.config();

const zohoConfig = {
  // OAuth 2.0 Configuration
  clientId: process.env.ZOHO_CLIENT_ID,
  clientSecret: process.env.ZOHO_CLIENT_SECRET,
  redirectUri: process.env.ZOHO_REDIRECT_URI || 'https://49c43afed0d6.ngrok-free.app/zoho/callback',
  
  // Organization Configuration
  organizationId: process.env.ZOHO_ORGANIZATION_ID,
  
  // API Configuration
  baseUrl: process.env.ZOHO_BASE_URL || 'https://www.zohoapis.com',
  accountsUrl: process.env.ZOHO_ACCOUNTS_URL || 'https://accounts.zoho.com',
  
  // Scopes required for Zoho Books API
  scopes: [
    'ZohoBooks.contacts.CREATE',
    'ZohoBooks.contacts.READ',
    'ZohoBooks.contacts.UPDATE',
    'ZohoBooks.items.CREATE',
    'ZohoBooks.items.READ',
    'ZohoBooks.items.UPDATE',
    'ZohoBooks.invoices.CREATE',
    'ZohoBooks.invoices.READ',
    'ZohoBooks.invoices.UPDATE',
    'ZohoBooks.bills.CREATE',
    'ZohoBooks.bills.READ',
    'ZohoBooks.bills.UPDATE',
    'ZohoBooks.organizations.READ'
  ].join(','),
  
  // API Endpoints
  endpoints: {
    contacts: '/books/v3/contacts',
    items: '/books/v3/items',
    invoices: '/books/v3/invoices',
    bills: '/books/v3/bills',
    organizations: '/books/v3/organizations'
  },
  
  // Rate limiting configuration
  rateLimit: {
    requestsPerMinute: 100,
    requestsPerDay: 1000
  },
  
  // Retry configuration
  retry: {
    maxAttempts: 3,
    backoffMultiplier: 2,
    initialDelay: 1000
  }
};

// Validation
const validateConfig = () => {
  const requiredFields = ['clientId', 'clientSecret', 'organizationId'];
  const missingFields = requiredFields.filter(field => !zohoConfig[field]);
  
  if (missingFields.length > 0) {
    throw new Error(`Missing required Zoho Books configuration: ${missingFields.join(', ')}`);
  }
};

module.exports = {
  zohoConfig,
  validateConfig
};
