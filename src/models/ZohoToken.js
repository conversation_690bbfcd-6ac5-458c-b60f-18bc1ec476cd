const mongoose = require('mongoose');

const zohoTokenSchema = new mongoose.Schema({
  // Token Information
  accessToken: {
    type: String,
    required: true,
    trim: true
  },
  refreshToken: {
    type: String,
    required: true,
    trim: true
  },
  tokenType: {
    type: String,
    default: 'Bearer',
    trim: true
  },
  
  // Token Expiry
  expiresIn: {
    type: Number,
    required: true // in seconds
  },
  expiresAt: {
    type: Date,
    required: true
  },
  
  // Scope Information
  scope: {
    type: String,
    trim: true
  },
  
  // Organization Information
  organizationId: {
    type: String,
    required: true,
    trim: true
  },
  
  // Token Status
  isActive: {
    type: Boolean,
    default: true
  },
  
  // Usage Tracking
  lastUsed: {
    type: Date,
    default: Date.now
  },
  usageCount: {
    type: Number,
    default: 0
  },
  
  // Error Tracking
  lastError: {
    type: String,
    trim: true
  },
  errorCount: {
    type: Number,
    default: 0
  },
  
  // Metadata
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  }
}, { 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
zohoTokenSchema.index({ organizationId: 1 });
zohoTokenSchema.index({ isActive: 1 });
zohoTokenSchema.index({ expiresAt: 1 });

// Virtual to check if token is expired
zohoTokenSchema.virtual('isExpired').get(function() {
  return new Date() >= this.expiresAt;
});

// Virtual to check if token needs refresh (expires in next 5 minutes)
zohoTokenSchema.virtual('needsRefresh').get(function() {
  const fiveMinutesFromNow = new Date(Date.now() + 5 * 60 * 1000);
  return fiveMinutesFromNow >= this.expiresAt;
});

// Method to update token usage
zohoTokenSchema.methods.recordUsage = function() {
  this.lastUsed = new Date();
  this.usageCount += 1;
  return this.save();
};

// Method to record error
zohoTokenSchema.methods.recordError = function(error) {
  this.lastError = error.message || 'Unknown error';
  this.errorCount += 1;
  return this.save();
};

// Method to update token
zohoTokenSchema.methods.updateToken = function(tokenData) {
  this.accessToken = tokenData.access_token;
  this.refreshToken = tokenData.refresh_token;
  this.expiresIn = tokenData.expires_in;
  this.expiresAt = new Date(Date.now() + tokenData.expires_in * 1000);
  this.scope = tokenData.scope;
  this.isActive = true;
  this.lastError = null;
  
  return this.save();
};

// Static method to get active token for organization
zohoTokenSchema.statics.getActiveToken = async function(organizationId) {
  return this.findOne({
    organizationId,
    isActive: true
  }).sort({ createdAt: -1 });
};

// Static method to deactivate all tokens for organization
zohoTokenSchema.statics.deactivateTokens = async function(organizationId) {
  return this.updateMany(
    { organizationId },
    { isActive: false }
  );
};

const ZohoToken = mongoose.model('ZohoToken', zohoTokenSchema);

module.exports = ZohoToken;
