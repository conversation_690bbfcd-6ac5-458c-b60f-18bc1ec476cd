const mongoose = require('mongoose');

const zohoSyncSchema = new mongoose.Schema({
  // Entity Information
  entityType: {
    type: String,
    enum: ['CONTACT', 'ITEM', 'INVOICE', 'BILL'],
    required: true
  },
  entitySubType: {
    type: String,
    enum: [
      // Contact subtypes
      'TEMPLE_VENDOR', 'PRODUCT_VENDOR', 'CUSTOMER',
      // Item subtypes  
      'TEMPLE_SERVICE', 'PRODUCT', 'PRODUCT_VARIANT',
      // Invoice subtypes
      'BOOKING_INVOICE', 'ORDER_INVOICE',
      // Bill subtypes
      'VENDOR_SETTLEMENT', 'COMMISSION_BILL'
    ],
    required: true
  },
  
  // Local Entity Reference
  localEntityId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    index: true
  },
  localEntityModel: {
    type: String,
    enum: ['Temple', 'Vendor', 'User', 'PoojaSchedule', 'DarshanSchedule', 'Event', 'Product', 'ProductVariant', 'Booking', 'Order'],
    required: true
  },
  
  // Zoho Books Information
  zohoEntityId: {
    type: String,
    trim: true,
    index: true
  },
  zohoEntityNumber: {
    type: String,
    trim: true
  },
  
  // Sync Status
  syncStatus: {
    type: String,
    enum: ['PENDING', 'IN_PROGRESS', 'SUCCESS', 'FAILED', 'RETRY'],
    default: 'PENDING',
    index: true
  },
  
  // Sync Operation Details
  operation: {
    type: String,
    enum: ['CREATE', 'UPDATE', 'DELETE'],
    required: true
  },
  
  // Error Information
  errorMessage: {
    type: String,
    trim: true
  },
  errorCode: {
    type: String,
    trim: true
  },
  retryCount: {
    type: Number,
    default: 0,
    min: 0
  },
  maxRetries: {
    type: Number,
    default: 3,
    min: 0
  },
  
  // Timestamps
  lastSyncAttempt: {
    type: Date,
    default: Date.now
  },
  lastSuccessfulSync: {
    type: Date
  },
  nextRetryAt: {
    type: Date
  },
  
  // Sync Data
  syncData: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  
  // Response from Zoho
  zohoResponse: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  
  // Metadata
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  },
  notes: {
    type: String,
    trim: true
  }
}, { 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
zohoSyncSchema.index({ entityType: 1, entitySubType: 1 });
zohoSyncSchema.index({ localEntityId: 1, localEntityModel: 1 });
zohoSyncSchema.index({ syncStatus: 1, nextRetryAt: 1 });
zohoSyncSchema.index({ createdAt: 1 });

// Virtual for checking if retry is needed
zohoSyncSchema.virtual('needsRetry').get(function() {
  return this.syncStatus === 'FAILED' && 
         this.retryCount < this.maxRetries && 
         (!this.nextRetryAt || this.nextRetryAt <= new Date());
});

// Method to increment retry count and set next retry time
zohoSyncSchema.methods.scheduleRetry = function() {
  this.retryCount += 1;
  this.syncStatus = 'RETRY';
  
  // Exponential backoff: 1min, 2min, 4min, etc.
  const delayMinutes = Math.pow(2, this.retryCount - 1);
  this.nextRetryAt = new Date(Date.now() + delayMinutes * 60 * 1000);
  
  return this.save();
};

// Method to mark sync as successful
zohoSyncSchema.methods.markSuccess = function(zohoResponse) {
  this.syncStatus = 'SUCCESS';
  this.lastSuccessfulSync = new Date();
  this.errorMessage = null;
  this.errorCode = null;
  this.zohoResponse = zohoResponse;
  
  return this.save();
};

// Method to mark sync as failed
zohoSyncSchema.methods.markFailed = function(error) {
  this.syncStatus = 'FAILED';
  this.errorMessage = error.message || 'Unknown error';
  this.errorCode = error.code || 'UNKNOWN_ERROR';
  this.lastSyncAttempt = new Date();
  
  return this.save();
};

const ZohoSync = mongoose.model('ZohoSync', zohoSyncSchema);

module.exports = ZohoSync;
