const mongoose = require('mongoose');

const musicSchema = new mongoose.Schema({
  title: <PERSON><PERSON><PERSON>,
  artist: <PERSON><PERSON><PERSON>,
  image: {
    type: String,
    required: true,
    trim: true,
  },
  language: {
    type: String,
    default: 'Hindi',
    trim: true,
  },
  duration: {
    type: Number, 
    default: 0,
  },
  s3Key: {
    type: String,
    required: true,
    trim: true,
  },
  fileSize: {
    type: Number,
    default: 0,
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  }
},
{ timestamps: true }
);

const Music = mongoose.model('Music', musicSchema);

module.exports = Music;