const zohoAuth = require('../../services/zohoAuth');
const { apiResponse, errorApiResponse } = require('../../config/responseHandler');

/**
 * Handle OAuth callback from Zoho Books (public endpoint)
 */
const handleCallback = async (req, res) => {
  try {
    const { code, state, error } = req.query;
    
    if (error) {
      return res.redirect(`/admin/zoho-books/auth?error=${encodeURIComponent(error)}`);
    }
    
    if (!code) {
      return res.redirect('/admin/zoho-books/auth?error=no_code');
    }
    
    // Use state as admin ID (this was set during auth URL generation)
    const adminId = state;
    
    if (!adminId) {
      return res.redirect('/admin/zoho-books/auth?error=invalid_state');
    }
    
    const result = await zohoAuth.exchangeCodeForToken(code, adminId);
    
    if (result.success) {
      return res.redirect('/admin/zoho-books/auth?success=true');
    } else {
      return res.redirect(`/admin/zoho-books/auth?error=${encodeURIComponent(result.error || 'Authentication failed')}`);
    }
    
  } catch (error) {
    console.error('Zoho callback error:', error);
    return res.redirect(`/admin/zoho-books/auth?error=${encodeURIComponent(error.message)}`);
  }
};

module.exports = {
  handleCallback
};
