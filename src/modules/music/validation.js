const joi = require('joi');
const mongoose = require('mongoose');

const uploadUrlSchema = joi.object({
  extension: joi.string()
    .valid('jpg', 'jpeg', 'png', 'heic', 'heif','pdf', 'webp', 'mp4', 'mov', 'hevc', 'flv', 'webm', 'mpeg', 'mp3')
    .required()
    .messages({
      'any.required': 'File extension is required',
      'any.only': 'Invalid file extension. Only JPEG, JPG, PNG, HEIC, HEIF, PDF, WEBP, MP4, MOV, HEVC, FLV, WEBM, MPEG, MP3 are allowed'
    })
});

const saveMusicSchema = joi.object({
  title: joi.string()
    .trim()
    .required()
    .messages({
      'any.required': 'Title is required',
      'string.empty': 'Title cannot be empty'
    }),

  artist: joi.string()
    .trim()
    .required()
    .messages({
      'any.required': 'Artist is required',
      'string.empty': 'Artist cannot be empty'
    }),

  image: joi.string()
    .trim()
    .required()
    .messages({
      'any.required': 'Image is required',
      'string.empty': 'Image key cannot be empty',
      'string.uri': 'Image must be a valid S3 key'
    }),

  language: joi.string()
    .trim()
    .default('Hindi')
    .messages({
      'string.base': 'Language must be a string'
    }),

  duration: joi.number()
    .integer()
    .min(0)
    .required()
    .messages({
      'any.required': 'Duration is required',
      'number.base': 'Duration must be a number',
      'number.min': 'Duration cannot be negative'
    }),

  s3Key: joi.string()
    .trim()
    .required()
    .messages({
      'any.required': 'S3 key is required',
      'string.empty': 'S3 key cannot be empty'
    }),

  fileSize: joi.number()
    .integer()
    .min(0)
    .required()
    .messages({
      'any.required': 'File size is required',
      'number.base': 'File size must be a number',
      'number.min': 'File size cannot be negative'
    }),
});

const listSongsSchema = joi.object({
  page: joi.number()
    .integer()
    .min(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.min': 'Page must be at least 1'
    }),

  limit: joi.number()
    .integer()
    .min(1)
    .max(200)
    .messages({
      'number.base': 'Limit must be a number',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100'
    }),

  search: joi.string()
    .trim()
    .allow('')
    .optional()
    .messages({
      'string.base': 'Search must be a string'
    }),

  language: joi.string()
    .trim()
    .optional()
    .messages({
      'string.base': 'Language must be a string'
    }),

  artist: joi.string()
    .trim()
    .optional()
    .messages({
      'string.base': 'Artist must be a string'
    }),

  sortBy: joi.string()
    .valid('title', 'artist', 'duration', 'fileSize', 'createdAt', 'updatedAt')
    .default('createdAt')
    .messages({
      'any.only': 'Invalid sort field'
    }),

  sortOrder: joi.string()
    .valid('1', '-1')
    .messages({
      'any.only': 'Sort order must be 1 or -1'
    })
});

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

const validateId = joi.string().custom(validateObjectId).required().messages({
  'any.invalid': 'Invalid song ID format',
  'any.required': 'Song ID is required'
});

module.exports = {
  uploadUrlSchema,
  saveMusicSchema,
  listSongsSchema,
  validateId
};