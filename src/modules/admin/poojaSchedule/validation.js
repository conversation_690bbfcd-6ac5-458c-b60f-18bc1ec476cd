const joi = require('joi');

const timeSlotSchema = joi.object({
  startTime: joi.string()
    .pattern(/^(0[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/)
    .required()
    .messages({
      'string.pattern.base': 'Invalid time format. Use HH:MM AM/PM format'
    }),
  endTime: joi.string()
    .pattern(/^(0[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/)
    .required()
    .messages({
      'string.pattern.base': 'Invalid time format. Use HH:MM AM/PM format'
    })
});

const createPoojaScheduleSchema = joi.object({
  temple: joi.string()
    .pattern(/^[0-9a-fA-F]{24}$/)
    .required()
    .messages({
      'string.pattern.base': 'Invalid temple ID format'
    }),
  name: joi.string(),
  duration: joi.object({
    value: joi.number().integer().min(1),
    unit: joi.string().valid('MINS', 'HOURS', 'ALL_NIGHT')
  }).optional(),
  type: joi.string()
    .valid('PHYSICAL', 'VIRTUAL')
    .required(),
  description: joi.string()
    .min(50)
    .required()
    .messages({
      'string.min': 'Description must be at least 50 characters long'
    }),
  dateType: joi.string()
    .valid('SPECIFIC_DATE', 'DATE_RANGE')
    .required(),
  specificDate: joi.when('dateType', {
    is: 'SPECIFIC_DATE',
    // then: joi.date().greater('now').required(),
    then: joi.date().required(),
    otherwise: joi.forbidden()
  }),
  dateRange: joi.when('dateType', {
    is: 'DATE_RANGE',
    then: joi.object({
      startDate: joi.date().required(),
      endDate: joi.date().greater(joi.ref('startDate')).required()
    }).required(),
    otherwise: joi.forbidden()
  }),
  timeSlots: joi.array()
    .items(timeSlotSchema)
    .min(1)
    .required()
    .messages({
      'array.min': 'Please add at least one time slot'
    }),
  pricing: joi.object({
    gstPercentage: joi.number().min(0).optional(),
    individual: joi.number().min(0).required(),
    individualTaxablePrice: joi.number().min(0).optional(),
    individualGstPrice: joi.number().min(0).optional(),
    couple: joi.number().min(0).required(),
    coupleTaxablePrice: joi.number().min(0).optional(),
    coupleGstPrice: joi.number().min(0).optional(),
    family: joi.number().min(0).required(),
    familyTaxablePrice: joi.number().min(0).optional(),
    familyGstPrice: joi.number().min(0).optional(),
  }).required(),
  promotionalKit: joi.object({
    gstPercentage: joi.number().min(0).optional(),
    price: joi.number().min(0).optional(),
    taxablePrice: joi.number().min(0).optional(),
    gstPrice: joi.number().min(0).optional(),
  }).allow(null).optional(),
  occupancyPerSlot: joi.number().integer().positive().optional(),
  guideline: joi.string().required().messages({
    'any.required': 'Guideline is required for this puja',
    'string.empty': 'Guideline cannot be an empty string'
  })
});

const updatePoojaScheduleSchema = createPoojaScheduleSchema.fork(
  [ 'temple', 'name', 'type', 'duration', 'description', 'dateType', 'specificDate', 'dateRange', 'timeSlots', 'pricing', 'occupancyPerSlot', 'occupancyPerSlot', 'guideline' ],
  (schema) => schema.optional()
).min(1);

module.exports = {
  createPoojaScheduleSchema,
  updatePoojaScheduleSchema
};
