const joi = require('joi');

const syncEntitySchema = joi.object({
  entityType: joi.string().valid(
    'TEMPLE_CONTACT',
    'VENDOR_CONTACT', 
    'USER_CONTACT',
    'POOJA_ITEM',
    'DARSHAN_ITEM',
    'EVENT_ITEM',
    'PRODUCT_ITEM',
    'VARIANT_ITEM',
    'BOOKING_INVOICE',
    'ORDER_INVOICE'
  ).required(),
  entityId: joi.string().required(),
  operation: joi.string().valid('CREATE', 'UPDATE').default('CREATE')
});

const retrySyncsSchema = joi.object({
  syncIds: joi.array().items(joi.string()).optional()
});

const getSyncStatusSchema = joi.object({
  entityType: joi.string().valid('CONTACT', 'ITEM', 'INVOICE', 'BILL').optional(),
  syncStatus: joi.string().valid('PENDING', 'IN_PROGRESS', 'SUCCESS', 'FAILED', 'RETRY').optional(),
  page: joi.number().integer().min(1).default(1),
  limit: joi.number().integer().min(1).max(100).default(50)
});

const paginationSchema = joi.object({
  page: joi.number().integer().min(1).default(1),
  limit: joi.number().integer().min(1).max(100).default(50)
});

module.exports = {
  syncEntitySchema,
  retrySyncsSchema,
  getSyncStatusSchema,
  paginationSchema
};
