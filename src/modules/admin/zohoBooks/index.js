const express = require('express');
const router = express.Router();
const zohoController = require('./controller');
const auth = require('../../../middleware/auth');
const { isAdminOrSuperAdmin } = require('../../../middleware/roleCheck');

// Apply authentication and role check to all routes
router.use(auth);
router.use(isAdminOrSuperAdmin);

// ==================== AUTHENTICATION ROUTES ====================

/**
 * @route GET /api/v1/admin/zoho-books/auth/url
 * @desc Get Zoho Books authorization URL
 * @access Admin/SuperAdmin
 */
router.get('/auth/url', zohoController.getAuthUrl);

/**
 * @route GET /api/v1/admin/zoho-books/auth/callback
 * @desc Handle OAuth callback from Zoho Books
 * @access Admin/SuperAdmin
 */
router.get('/auth/callback', zohoController.handleCallback);

/**
 * @route GET /api/v1/admin/zoho-books/auth/status
 * @desc Get authentication status
 * @access Admin/SuperAdmin
 */
router.get('/auth/status', zohoController.getAuthStatus);

/**
 * @route DELETE /api/v1/admin/zoho-books/auth/revoke
 * @desc Revoke Zoho Books authentication
 * @access Admin/SuperAdmin
 */
router.delete('/auth/revoke', zohoController.revokeAuth);

// ==================== SYNC OPERATION ROUTES ====================

/**
 * @route POST /api/v1/admin/zoho-books/sync/contacts
 * @desc Sync all contacts (temples and vendors) to Zoho Books
 * @access Admin/SuperAdmin
 */
router.post('/sync/contacts', zohoController.syncAllContacts);

/**
 * @route POST /api/v1/admin/zoho-books/sync/items
 * @desc Sync all items (temple services and products) to Zoho Books
 * @access Admin/SuperAdmin
 */
router.post('/sync/items', zohoController.syncAllItems);

/**
 * @route POST /api/v1/admin/zoho-books/sync/invoices
 * @desc Sync all invoices (bookings and orders) to Zoho Books
 * @access Admin/SuperAdmin
 */
router.post('/sync/invoices', zohoController.syncAllInvoices);

/**
 * @route POST /api/v1/admin/zoho-books/sync/entity
 * @desc Sync specific entity to Zoho Books
 * @body {entityType, entityId, operation}
 * @access Admin/SuperAdmin
 */
router.post('/sync/entity', zohoController.syncEntity);

// ==================== MONITORING ROUTES ====================

/**
 * @route GET /api/v1/admin/zoho-books/sync/status
 * @desc Get sync status overview
 * @query {entityType, syncStatus, page, limit}
 * @access Admin/SuperAdmin
 */
router.get('/sync/status', zohoController.getSyncStatus);

/**
 * @route GET /api/v1/admin/zoho-books/sync/failed
 * @desc Get failed sync records
 * @query {page, limit}
 * @access Admin/SuperAdmin
 */
router.get('/sync/failed', zohoController.getFailedSyncs);

/**
 * @route POST /api/v1/admin/zoho-books/sync/retry
 * @desc Retry failed sync operations
 * @body {syncIds} - Optional array of specific sync IDs to retry
 * @access Admin/SuperAdmin
 */
router.post('/sync/retry', zohoController.retryFailedSyncs);

module.exports = router;
