const zohoAuth = require('../../../services/zohoAuth');
const zohoBooksService = require('../../../services/zohoBooks');
const zohoContactSync = require('../../../services/zohoContactSync');
const zohoItemSync = require('../../../services/zohoItemSync');
const zohoInvoiceSync = require('../../../services/zohoInvoiceSync');
const ZohoSync = require('../../../models/ZohoSync');
const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { messages } = require('../../../messages');

// ==================== AUTHENTICATION ====================

/**
 * Get Zoho Books authorization URL
 */
const getAuthUrl = async (req, res) => {
  try {
    const state = req.user.id; // Use admin ID as state for security
    const authUrl = zohoAuth.generateAuthUrl(state);
    
    return apiResponse({
      res,
      code: 200,
      status: true,
      message: 'Authorization URL generated successfully',
      data: { authUrl }
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Handle OAuth callback
 */
const handleCallback = async (req, res) => {
  try {
    const { code, state } = req.query;
    
    if (!code) {
      throw new Error('Authorization code not provided');
    }
    
    const result = await zohoAuth.exchangeCodeForToken(code, req.user.id);
    
    return apiResponse({
      res,
      code: 200,
      status: true,
      message: result.message,
      data: {
        authenticated: true,
        expiresAt: result.token.expiresAt
      }
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get authentication status
 */
const getAuthStatus = async (req, res) => {
  try {
    const status = await zohoAuth.getAuthStatus();
    
    return apiResponse({
      res,
      code: 200,
      status: true,
      message: 'Authentication status retrieved',
      data: status
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Revoke authentication
 */
const revokeAuth = async (req, res) => {
  try {
    const token = await zohoAuth.getValidAccessToken();
    await zohoAuth.revokeToken(token);
    
    return apiResponse({
      res,
      code: 200,
      status: true,
      message: 'Authentication revoked successfully',
      data: { authenticated: false }
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

// ==================== SYNC OPERATIONS ====================

/**
 * Sync all contacts (temples and vendors)
 */
const syncAllContacts = async (req, res) => {
  try {
    const templeResults = await zohoContactSync.syncAllTemples();
    const vendorResults = await zohoContactSync.syncAllVendors();
    
    return apiResponse({
      res,
      code: 200,
      status: true,
      message: 'Contact sync completed',
      data: {
        temples: templeResults,
        vendors: vendorResults,
        total: templeResults.total + vendorResults.total,
        successful: templeResults.successful + vendorResults.successful,
        failed: templeResults.failed + vendorResults.failed
      }
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Sync all items (temple services and products)
 */
const syncAllItems = async (req, res) => {
  try {
    const serviceResults = await zohoItemSync.syncAllTempleServices();
    const productResults = await zohoItemSync.syncAllProducts();
    
    return apiResponse({
      res,
      code: 200,
      status: true,
      message: 'Item sync completed',
      data: {
        services: serviceResults,
        products: productResults,
        total: serviceResults.total + productResults.total,
        successful: serviceResults.successful + productResults.successful,
        failed: serviceResults.failed + productResults.failed
      }
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Sync all invoices (bookings and orders)
 */
const syncAllInvoices = async (req, res) => {
  try {
    const bookingResults = await zohoInvoiceSync.syncAllBookingInvoices();
    const orderResults = await zohoInvoiceSync.syncAllOrderInvoices();
    
    return apiResponse({
      res,
      code: 200,
      status: true,
      message: 'Invoice sync completed',
      data: {
        bookings: bookingResults,
        orders: orderResults,
        total: bookingResults.total + orderResults.total,
        successful: bookingResults.successful + orderResults.successful,
        failed: bookingResults.failed + orderResults.failed
      }
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Sync specific entity
 */
const syncEntity = async (req, res) => {
  try {
    const { entityType, entityId, operation = 'CREATE' } = req.body;
    
    let result;
    
    switch (entityType) {
      case 'TEMPLE_CONTACT':
        result = await zohoContactSync.syncTempleContact(entityId, operation);
        break;
      case 'VENDOR_CONTACT':
        result = await zohoContactSync.syncVendorContact(entityId, operation);
        break;
      case 'USER_CONTACT':
        result = await zohoContactSync.syncUserContact(entityId, operation);
        break;
      case 'POOJA_ITEM':
        result = await zohoItemSync.syncPoojaScheduleItem(entityId, operation);
        break;
      case 'DARSHAN_ITEM':
        result = await zohoItemSync.syncDarshanScheduleItem(entityId, operation);
        break;
      case 'EVENT_ITEM':
        result = await zohoItemSync.syncEventItem(entityId, operation);
        break;
      case 'PRODUCT_ITEM':
        result = await zohoItemSync.syncProductItem(entityId, operation);
        break;
      case 'VARIANT_ITEM':
        result = await zohoItemSync.syncProductVariantItem(entityId, operation);
        break;
      case 'BOOKING_INVOICE':
        result = await zohoInvoiceSync.syncBookingInvoice(entityId, operation);
        break;
      case 'ORDER_INVOICE':
        result = await zohoInvoiceSync.syncOrderInvoice(entityId, operation);
        break;
      default:
        throw new Error('Invalid entity type');
    }
    
    return apiResponse({
      res,
      code: 200,
      status: result.success,
      message: result.success ? 'Entity synced successfully' : 'Entity sync failed',
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

// ==================== SYNC STATUS & MONITORING ====================

/**
 * Get sync status overview
 */
const getSyncStatus = async (req, res) => {
  try {
    const { entityType, syncStatus, page = 1, limit = 50 } = req.query;
    
    const filter = {};
    if (entityType) filter.entityType = entityType;
    if (syncStatus) filter.syncStatus = syncStatus;
    
    const skip = (page - 1) * limit;
    
    const [records, total] = await Promise.all([
      ZohoSync.find(filter)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit))
        .populate('localEntityId'),
      ZohoSync.countDocuments(filter)
    ]);
    
    // Get summary statistics
    const summary = await ZohoSync.aggregate([
      { $group: {
        _id: { entityType: '$entityType', syncStatus: '$syncStatus' },
        count: { $sum: 1 }
      }},
      { $group: {
        _id: '$_id.entityType',
        statuses: { $push: { status: '$_id.syncStatus', count: '$count' } },
        total: { $sum: '$count' }
      }}
    ]);
    
    return apiResponse({
      res,
      code: 200,
      status: true,
      message: 'Sync status retrieved successfully',
      data: {
        records,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        },
        summary
      }
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get failed sync records
 */
const getFailedSyncs = async (req, res) => {
  try {
    const { page = 1, limit = 50 } = req.query;
    const skip = (page - 1) * limit;
    
    const [records, total] = await Promise.all([
      ZohoSync.find({ syncStatus: 'FAILED' })
        .sort({ lastSyncAttempt: -1 })
        .skip(skip)
        .limit(parseInt(limit)),
      ZohoSync.countDocuments({ syncStatus: 'FAILED' })
    ]);
    
    return apiResponse({
      res,
      code: 200,
      status: true,
      message: 'Failed sync records retrieved',
      data: {
        records,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Retry failed syncs
 */
const retryFailedSyncs = async (req, res) => {
  try {
    const { syncIds } = req.body;
    
    let query = { syncStatus: 'FAILED' };
    if (syncIds && syncIds.length > 0) {
      query._id = { $in: syncIds };
    }
    
    const failedRecords = await ZohoSync.find(query);
    const results = [];
    
    for (const record of failedRecords) {
      try {
        await record.scheduleRetry();
        results.push({
          syncId: record._id,
          success: true,
          message: 'Retry scheduled'
        });
      } catch (error) {
        results.push({
          syncId: record._id,
          success: false,
          error: error.message
        });
      }
    }
    
    return apiResponse({
      res,
      code: 200,
      status: true,
      message: 'Retry operation completed',
      data: {
        total: results.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length,
        results
      }
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  // Authentication
  getAuthUrl,
  handleCallback,
  getAuthStatus,
  revokeAuth,
  
  // Sync Operations
  syncAllContacts,
  syncAllItems,
  syncAllInvoices,
  syncEntity,
  
  // Monitoring
  getSyncStatus,
  getFailedSyncs,
  retryFailedSyncs
};
