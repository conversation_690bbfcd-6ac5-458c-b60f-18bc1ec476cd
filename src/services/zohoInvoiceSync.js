const zohoBooksService = require('./zohoBooks');
const zohoContactSync = require('./zohoContactSync');
const zohoItemSync = require('./zohoItemSync');
const Booking = require('../models/Booking');
const Order = require('../models/Order');
const moment = require('moment');

class ZohoInvoiceSyncService {

  // ==================== BOOKING INVOICE SYNC ====================

  /**
   * Map Booking to Zoho invoice format
   * @param {Object} booking - Booking document
   * @param {string} customerContactId - Zoho customer contact ID
   * @returns {Object} Zoho invoice data
   */
  async mapBookingToInvoice(booking, customerContactId) {
    const lineItems = [];

    // Main service line item
    const serviceItem = await this.getServiceItemForBooking(booking);
    if (serviceItem) {
      lineItems.push({
        item_id: serviceItem.zohoEntityId,
        name: serviceItem.name || 'Temple Service',
        description: `Booking for ${booking.type} - ${moment(booking.date).format('DD/MM/YYYY')}`,
        rate: booking.amount || 0,
        quantity: 1,
        tax_percentage: serviceItem.tax_percentage || 0
      });
    }

    // Promotional kit if applicable
    if (booking.promotionalKitCount && booking.promotionalKitCost) {
      lineItems.push({
        name: 'Promotional Kit',
        description: `Promotional kit for booking`,
        rate: booking.promotionalKitCost,
        quantity: booking.promotionalKitCount,
        tax_percentage: 0
      });
    }

    return {
      customer_id: customerContactId,
      invoice_number: `BK-${booking._id.toString().slice(-8).toUpperCase()}`,
      date: moment(booking.createdAt).format('YYYY-MM-DD'),
      due_date: moment(booking.createdAt).add(30, 'days').format('YYYY-MM-DD'),
      line_items: lineItems,
      discount: booking.discountAmount || 0,
      notes: `Temple booking for ${booking.type}\nBooking ID: ${booking._id}\nDate: ${moment(booking.date).format('DD/MM/YYYY')}`,
      terms: 'Payment for temple services',
      custom_fields: [
        {
          customfield_id: 'booking_id',
          value: booking._id.toString()
        },
        {
          customfield_id: 'booking_type',
          value: booking.type
        },
        {
          customfield_id: 'temple_id',
          value: booking.temple?.toString() || ''
        }
      ]
    };
  }

  /**
   * Get service item for booking
   * @param {Object} booking - Booking document
   * @returns {Object} Service item sync record
   */
  async getServiceItemForBooking(booking) {
    let entityModel, entityId;

    switch (booking.type) {
      case 'POOJA_BOOKING':
        entityModel = 'PoojaSchedule';
        entityId = booking.poojaSchedule;
        break;
      case 'DARSHAN_BOOKING':
        entityModel = 'DarshanSchedule';
        entityId = booking.darshanSchedule;
        break;
      case 'EVENT_BOOKING':
        entityModel = 'Event';
        entityId = booking.event;
        break;
      default:
        return null;
    }

    if (!entityId) return null;

    return await zohoBooksService.getSyncRecord(entityId, entityModel, 'ITEM');
  }

  /**
   * Sync booking as invoice to Zoho Books
   * @param {string} bookingId - Booking ID
   * @param {string} operation - CREATE or UPDATE
   * @returns {Object} Sync result
   */
  async syncBookingInvoice(bookingId, operation = 'CREATE') {
    try {
      const booking = await Booking.findById(bookingId)
        .populate('user temple poojaSchedule darshanSchedule event');
      
      if (!booking) {
        throw new Error('Booking not found');
      }

      // Ensure customer contact exists in Zoho
      let customerSync = await zohoBooksService.getSyncRecord(booking.user._id, 'User', 'CONTACT');
      if (!customerSync || !customerSync.zohoEntityId) {
        const contactResult = await zohoContactSync.syncUserContact(booking.user._id, 'CREATE');
        if (!contactResult.success) {
          throw new Error(`Failed to sync customer contact: ${contactResult.error}`);
        }
        customerSync = contactResult.syncRecord;
      }

      // Create sync record
      const syncRecord = await zohoBooksService.createSyncRecord({
        entityType: 'INVOICE',
        entitySubType: 'BOOKING_INVOICE',
        localEntityId: bookingId,
        localEntityModel: 'Booking',
        operation,
        syncStatus: 'IN_PROGRESS',
        syncData: await this.mapBookingToInvoice(booking, customerSync.zohoEntityId)
      });

      let zohoInvoice;
      
      if (operation === 'CREATE') {
        zohoInvoice = await zohoBooksService.createInvoice(syncRecord.syncData);
      } else {
        if (!syncRecord.zohoEntityId) {
          throw new Error('Zoho invoice ID not found for update operation');
        }
        zohoInvoice = await zohoBooksService.updateInvoice(syncRecord.zohoEntityId, syncRecord.syncData);
      }

      syncRecord.zohoEntityId = zohoInvoice.invoice_id;
      syncRecord.zohoEntityNumber = zohoInvoice.invoice_number;
      await syncRecord.markSuccess(zohoInvoice);

      return {
        success: true,
        zohoInvoice,
        syncRecord
      };

    } catch (error) {
      console.error('Error syncing booking invoice:', error);
      
      const syncRecord = await zohoBooksService.getSyncRecord(bookingId, 'Booking', 'INVOICE');
      if (syncRecord) {
        await syncRecord.markFailed(error);
      }

      return {
        success: false,
        error: error.message
      };
    }
  }

  // ==================== ORDER INVOICE SYNC ====================

  /**
   * Map Order to Zoho invoice format
   * @param {Object} order - Order document
   * @param {string} customerContactId - Zoho customer contact ID
   * @returns {Object} Zoho invoice data
   */
  async mapOrderToInvoice(order, customerContactId) {
    const lineItems = [];

    // Add order items
    for (const item of order.items) {
      // Get product/variant sync record
      let productSync;
      if (item.variantId) {
        productSync = await zohoBooksService.getSyncRecord(item.variantId, 'ProductVariant', 'ITEM');
      } else {
        productSync = await zohoBooksService.getSyncRecord(item.productId, 'Product', 'ITEM');
      }

      lineItems.push({
        item_id: productSync?.zohoEntityId || null,
        name: item.name || 'Product',
        description: item.description || '',
        rate: item.price || 0,
        quantity: item.quantity || 1,
        tax_percentage: item.gstPercentage || 0
      });
    }

    return {
      customer_id: customerContactId,
      invoice_number: `ORD-${order._id.toString().slice(-8).toUpperCase()}`,
      date: moment(order.createdAt).format('YYYY-MM-DD'),
      due_date: moment(order.createdAt).add(30, 'days').format('YYYY-MM-DD'),
      line_items: lineItems,
      discount: order.discountAmount || 0,
      notes: `E-shop order\nOrder ID: ${order._id}\nPayment Status: ${order.paymentStatus}`,
      terms: 'Payment for e-shop products',
      custom_fields: [
        {
          customfield_id: 'order_id',
          value: order._id.toString()
        },
        {
          customfield_id: 'order_status',
          value: order.status
        },
        {
          customfield_id: 'payment_status',
          value: order.paymentStatus
        }
      ]
    };
  }

  /**
   * Sync order as invoice to Zoho Books
   * @param {string} orderId - Order ID
   * @param {string} operation - CREATE or UPDATE
   * @returns {Object} Sync result
   */
  async syncOrderInvoice(orderId, operation = 'CREATE') {
    try {
      const order = await Order.findById(orderId).populate('user');
      if (!order) {
        throw new Error('Order not found');
      }

      // Ensure customer contact exists in Zoho
      let customerSync = await zohoBooksService.getSyncRecord(order.user._id, 'User', 'CONTACT');
      if (!customerSync || !customerSync.zohoEntityId) {
        const contactResult = await zohoContactSync.syncUserContact(order.user._id, 'CREATE');
        if (!contactResult.success) {
          throw new Error(`Failed to sync customer contact: ${contactResult.error}`);
        }
        customerSync = contactResult.syncRecord;
      }

      const syncRecord = await zohoBooksService.createSyncRecord({
        entityType: 'INVOICE',
        entitySubType: 'ORDER_INVOICE',
        localEntityId: orderId,
        localEntityModel: 'Order',
        operation,
        syncStatus: 'IN_PROGRESS',
        syncData: await this.mapOrderToInvoice(order, customerSync.zohoEntityId)
      });

      let zohoInvoice;
      
      if (operation === 'CREATE') {
        zohoInvoice = await zohoBooksService.createInvoice(syncRecord.syncData);
      } else {
        if (!syncRecord.zohoEntityId) {
          throw new Error('Zoho invoice ID not found for update operation');
        }
        zohoInvoice = await zohoBooksService.updateInvoice(syncRecord.zohoEntityId, syncRecord.syncData);
      }

      syncRecord.zohoEntityId = zohoInvoice.invoice_id;
      syncRecord.zohoEntityNumber = zohoInvoice.invoice_number;
      await syncRecord.markSuccess(zohoInvoice);

      return {
        success: true,
        zohoInvoice,
        syncRecord
      };

    } catch (error) {
      console.error('Error syncing order invoice:', error);
      
      const syncRecord = await zohoBooksService.getSyncRecord(orderId, 'Order', 'INVOICE');
      if (syncRecord) {
        await syncRecord.markFailed(error);
      }

      return {
        success: false,
        error: error.message
      };
    }
  }

  // ==================== BULK SYNC OPERATIONS ====================

  /**
   * Sync all paid bookings as invoices
   * @returns {Object} Bulk sync result
   */
  async syncAllBookingInvoices() {
    const bookings = await Booking.find({ 
      paymentStatus: 'PAID',
      status: { $in: ['CONFIRMED', 'COMPLETED'] }
    });
    
    const results = [];

    for (const booking of bookings) {
      const result = await this.syncBookingInvoice(booking._id, 'CREATE');
      results.push({
        bookingId: booking._id,
        type: booking.type,
        ...result
      });
    }

    return {
      total: bookings.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      results
    };
  }

  /**
   * Sync all paid orders as invoices
   * @returns {Object} Bulk sync result
   */
  async syncAllOrderInvoices() {
    const orders = await Order.find({ 
      paymentStatus: 'PAID',
      status: { $in: ['CONFIRMED', 'SHIPPED', 'DELIVERED'] }
    });
    
    const results = [];

    for (const order of orders) {
      const result = await this.syncOrderInvoice(order._id, 'CREATE');
      results.push({
        orderId: order._id,
        status: order.status,
        ...result
      });
    }

    return {
      total: orders.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      results
    };
  }
}

module.exports = new ZohoInvoiceSyncService();
