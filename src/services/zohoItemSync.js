const zohoBooksService = require('./zohoBooks');
const PoojaSchedule = require('../models/PoojaSchedule');
const DarshanSchedule = require('../models/DarshanSchedule');
const Event = require('../models/Event');
const Product = require('../models/Product');
const ProductVariant = require('../models/ProductVariant');

class ZohoItemSyncService {

  // ==================== TEMPLE SERVICE SYNC ====================

  /**
   * Map Pooja Schedule to Zoho item format
   * @param {Object} poojaSchedule - PoojaSchedule document
   * @returns {Object} Zoho item data
   */
  mapPoojaScheduleToItem(poojaSchedule) {
    const basePrice = Math.min(
      poojaSchedule.pricing.individual || 0,
      poojaSchedule.pricing.couple || 0,
      poojaSchedule.pricing.family || 0
    );

    return {
      name: poojaSchedule.name?.en || poojaSchedule.name?.hi || 'Pooja Service',
      description: poojaSchedule.description?.en || poojaSchedule.description?.hi || '',
      rate: basePrice,
      tax_percentage: poojaSchedule.pricing.gstPercentage || 0,
      item_type: 'service',
      product_type: 'service',
      category_name: 'Temple Services',
      sub_category_name: 'Pooja Services',
      sku: poojaSchedule.serviceCode || '',
      unit: 'nos',
      status: poojaSchedule.status === 'ACTIVE' ? 'active' : 'inactive',
      custom_fields: [
        {
          customfield_id: 'service_id',
          value: poojaSchedule._id.toString()
        },
        {
          customfield_id: 'service_type',
          value: 'POOJA_SCHEDULE'
        },
        {
          customfield_id: 'temple_id',
          value: poojaSchedule.temple?.toString() || ''
        },
        {
          customfield_id: 'service_code',
          value: poojaSchedule.serviceCode || ''
        }
      ]
    };
  }

  /**
   * Map Darshan Schedule to Zoho item format
   * @param {Object} darshanSchedule - DarshanSchedule document
   * @returns {Object} Zoho item data
   */
  mapDarshanScheduleToItem(darshanSchedule) {
    const basePrice = Math.min(
      darshanSchedule.pricing.individual || 0,
      darshanSchedule.pricing.couple || 0,
      darshanSchedule.pricing.family || 0
    );

    return {
      name: darshanSchedule.name?.en || darshanSchedule.name?.hi || 'Darshan Service',
      description: darshanSchedule.description?.en || darshanSchedule.description?.hi || '',
      rate: basePrice,
      tax_percentage: darshanSchedule.pricing.gstPercentage || 0,
      item_type: 'service',
      product_type: 'service',
      category_name: 'Temple Services',
      sub_category_name: 'Darshan Services',
      sku: darshanSchedule.serviceCode || '',
      unit: 'nos',
      status: darshanSchedule.status === 'ACTIVE' ? 'active' : 'inactive',
      custom_fields: [
        {
          customfield_id: 'service_id',
          value: darshanSchedule._id.toString()
        },
        {
          customfield_id: 'service_type',
          value: 'DARSHAN_SCHEDULE'
        },
        {
          customfield_id: 'temple_id',
          value: darshanSchedule.temple?.toString() || ''
        },
        {
          customfield_id: 'service_code',
          value: darshanSchedule.serviceCode || ''
        }
      ]
    };
  }

  /**
   * Map Event to Zoho item format
   * @param {Object} event - Event document
   * @returns {Object} Zoho item data
   */
  mapEventToItem(event) {
    // Calculate minimum price from event pricing
    let basePrice = 0;
    if (event.dateType === 'SPECIFIC_DATE') {
      basePrice = Math.min(
        event.pricing.individual || 0,
        event.pricing.couple || 0,
        event.pricing.family || 0
      );
    } else if (event.dateType === 'DATE_RANGE' && event.datePricing?.length > 0) {
      const minDatePrice = Math.min(...event.datePricing.map(dp => 
        Math.min(dp.individual || 0, dp.couple || 0, dp.family || 0)
      ));
      basePrice = minDatePrice;
    }

    return {
      name: event.name?.en || event.name?.hi || 'Temple Event',
      description: event.description?.en || event.description?.hi || '',
      rate: basePrice,
      tax_percentage: event.pricing?.gstPercentage || 0,
      item_type: 'service',
      product_type: 'service',
      category_name: 'Temple Services',
      sub_category_name: 'Events',
      sku: event.eventCode || '',
      unit: 'nos',
      status: event.status === 'ACTIVE' ? 'active' : 'inactive',
      custom_fields: [
        {
          customfield_id: 'service_id',
          value: event._id.toString()
        },
        {
          customfield_id: 'service_type',
          value: 'EVENT'
        },
        {
          customfield_id: 'temple_id',
          value: event.temple?.toString() || ''
        },
        {
          customfield_id: 'event_code',
          value: event.eventCode || ''
        }
      ]
    };
  }

  /**
   * Sync Pooja Schedule as item to Zoho Books
   * @param {string} poojaScheduleId - PoojaSchedule ID
   * @param {string} operation - CREATE or UPDATE
   * @returns {Object} Sync result
   */
  async syncPoojaScheduleItem(poojaScheduleId, operation = 'CREATE') {
    try {
      const poojaSchedule = await PoojaSchedule.findById(poojaScheduleId).populate('temple');
      if (!poojaSchedule) {
        throw new Error('Pooja Schedule not found');
      }

      const syncRecord = await zohoBooksService.createSyncRecord({
        entityType: 'ITEM',
        entitySubType: 'TEMPLE_SERVICE',
        localEntityId: poojaScheduleId,
        localEntityModel: 'PoojaSchedule',
        operation,
        syncStatus: 'IN_PROGRESS',
        syncData: this.mapPoojaScheduleToItem(poojaSchedule)
      });

      let zohoItem;
      
      if (operation === 'CREATE') {
        zohoItem = await zohoBooksService.createItem(syncRecord.syncData);
      } else {
        if (!syncRecord.zohoEntityId) {
          throw new Error('Zoho item ID not found for update operation');
        }
        zohoItem = await zohoBooksService.updateItem(syncRecord.zohoEntityId, syncRecord.syncData);
      }

      syncRecord.zohoEntityId = zohoItem.item_id;
      await syncRecord.markSuccess(zohoItem);

      return {
        success: true,
        zohoItem,
        syncRecord
      };

    } catch (error) {
      console.error('Error syncing pooja schedule item:', error);
      
      const syncRecord = await zohoBooksService.getSyncRecord(poojaScheduleId, 'PoojaSchedule', 'ITEM');
      if (syncRecord) {
        await syncRecord.markFailed(error);
      }

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Sync Darshan Schedule as item to Zoho Books
   * @param {string} darshanScheduleId - DarshanSchedule ID
   * @param {string} operation - CREATE or UPDATE
   * @returns {Object} Sync result
   */
  async syncDarshanScheduleItem(darshanScheduleId, operation = 'CREATE') {
    try {
      const darshanSchedule = await DarshanSchedule.findById(darshanScheduleId).populate('temple');
      if (!darshanSchedule) {
        throw new Error('Darshan Schedule not found');
      }

      const syncRecord = await zohoBooksService.createSyncRecord({
        entityType: 'ITEM',
        entitySubType: 'TEMPLE_SERVICE',
        localEntityId: darshanScheduleId,
        localEntityModel: 'DarshanSchedule',
        operation,
        syncStatus: 'IN_PROGRESS',
        syncData: this.mapDarshanScheduleToItem(darshanSchedule)
      });

      let zohoItem;
      
      if (operation === 'CREATE') {
        zohoItem = await zohoBooksService.createItem(syncRecord.syncData);
      } else {
        if (!syncRecord.zohoEntityId) {
          throw new Error('Zoho item ID not found for update operation');
        }
        zohoItem = await zohoBooksService.updateItem(syncRecord.zohoEntityId, syncRecord.syncData);
      }

      syncRecord.zohoEntityId = zohoItem.item_id;
      await syncRecord.markSuccess(zohoItem);

      return {
        success: true,
        zohoItem,
        syncRecord
      };

    } catch (error) {
      console.error('Error syncing darshan schedule item:', error);
      
      const syncRecord = await zohoBooksService.getSyncRecord(darshanScheduleId, 'DarshanSchedule', 'ITEM');
      if (syncRecord) {
        await syncRecord.markFailed(error);
      }

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Sync Event as item to Zoho Books
   * @param {string} eventId - Event ID
   * @param {string} operation - CREATE or UPDATE
   * @returns {Object} Sync result
   */
  async syncEventItem(eventId, operation = 'CREATE') {
    try {
      const event = await Event.findById(eventId).populate('temple');
      if (!event) {
        throw new Error('Event not found');
      }

      const syncRecord = await zohoBooksService.createSyncRecord({
        entityType: 'ITEM',
        entitySubType: 'TEMPLE_SERVICE',
        localEntityId: eventId,
        localEntityModel: 'Event',
        operation,
        syncStatus: 'IN_PROGRESS',
        syncData: this.mapEventToItem(event)
      });

      let zohoItem;
      
      if (operation === 'CREATE') {
        zohoItem = await zohoBooksService.createItem(syncRecord.syncData);
      } else {
        if (!syncRecord.zohoEntityId) {
          throw new Error('Zoho item ID not found for update operation');
        }
        zohoItem = await zohoBooksService.updateItem(syncRecord.zohoEntityId, syncRecord.syncData);
      }

      syncRecord.zohoEntityId = zohoItem.item_id;
      await syncRecord.markSuccess(zohoItem);

      return {
        success: true,
        zohoItem,
        syncRecord
      };

    } catch (error) {
      console.error('Error syncing event item:', error);
      
      const syncRecord = await zohoBooksService.getSyncRecord(eventId, 'Event', 'ITEM');
      if (syncRecord) {
        await syncRecord.markFailed(error);
      }

      return {
        success: false,
        error: error.message
      };
    }
  }

  // ==================== PRODUCT SYNC ====================

  /**
   * Map Product to Zoho item format
   * @param {Object} product - Product document
   * @returns {Object} Zoho item data
   */
  mapProductToItem(product) {
    return {
      name: product.name?.en || product.name?.hi || 'Product',
      description: product.description?.en || product.description?.hi || '',
      rate: product.price || 0,
      tax_percentage: product.gstPercentage || 0,
      item_type: 'inventory',
      product_type: 'goods',
      category_name: 'E-Shop Products',
      sku: product.productCode || product.sku || '',
      unit: 'nos',
      status: product.status === 'APPROVED' ? 'active' : 'inactive',
      track_quantity_and_price: true,
      initial_stock: product.stockQuantity || 0,
      custom_fields: [
        {
          customfield_id: 'product_id',
          value: product._id.toString()
        },
        {
          customfield_id: 'item_type',
          value: 'PRODUCT'
        },
        {
          customfield_id: 'vendor_id',
          value: product.createdBy?.toString() || ''
        },
        {
          customfield_id: 'product_code',
          value: product.productCode || ''
        }
      ]
    };
  }

  /**
   * Map Product Variant to Zoho item format
   * @param {Object} variant - ProductVariant document
   * @param {Object} product - Parent Product document
   * @returns {Object} Zoho item data
   */
  mapProductVariantToItem(variant, product) {
    const variantName = `${product.name?.en || product.name?.hi || 'Product'} - ${variant.variantName || 'Variant'}`;

    return {
      name: variantName,
      description: `${product.description?.en || product.description?.hi || ''} - Variant: ${variant.variantName || ''}`,
      rate: variant.price || 0,
      tax_percentage: product.gstPercentage || 0,
      item_type: 'inventory',
      product_type: 'goods',
      category_name: 'E-Shop Products',
      sub_category_name: 'Product Variants',
      sku: variant.sku || '',
      unit: 'nos',
      status: variant.status === 'ACTIVE' ? 'active' : 'inactive',
      track_quantity_and_price: true,
      initial_stock: variant.stockQuantity || 0,
      custom_fields: [
        {
          customfield_id: 'variant_id',
          value: variant._id.toString()
        },
        {
          customfield_id: 'product_id',
          value: product._id.toString()
        },
        {
          customfield_id: 'item_type',
          value: 'PRODUCT_VARIANT'
        },
        {
          customfield_id: 'vendor_id',
          value: product.createdBy?.toString() || ''
        },
        {
          customfield_id: 'variant_sku',
          value: variant.sku || ''
        }
      ]
    };
  }

  /**
   * Sync Product as item to Zoho Books
   * @param {string} productId - Product ID
   * @param {string} operation - CREATE or UPDATE
   * @returns {Object} Sync result
   */
  async syncProductItem(productId, operation = 'CREATE') {
    try {
      const product = await Product.findById(productId).populate('category subCategory');
      if (!product) {
        throw new Error('Product not found');
      }

      const syncRecord = await zohoBooksService.createSyncRecord({
        entityType: 'ITEM',
        entitySubType: 'PRODUCT',
        localEntityId: productId,
        localEntityModel: 'Product',
        operation,
        syncStatus: 'IN_PROGRESS',
        syncData: this.mapProductToItem(product)
      });

      let zohoItem;

      if (operation === 'CREATE') {
        zohoItem = await zohoBooksService.createItem(syncRecord.syncData);
      } else {
        if (!syncRecord.zohoEntityId) {
          throw new Error('Zoho item ID not found for update operation');
        }
        zohoItem = await zohoBooksService.updateItem(syncRecord.zohoEntityId, syncRecord.syncData);
      }

      syncRecord.zohoEntityId = zohoItem.item_id;
      await syncRecord.markSuccess(zohoItem);

      return {
        success: true,
        zohoItem,
        syncRecord
      };

    } catch (error) {
      console.error('Error syncing product item:', error);

      const syncRecord = await zohoBooksService.getSyncRecord(productId, 'Product', 'ITEM');
      if (syncRecord) {
        await syncRecord.markFailed(error);
      }

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Sync Product Variant as item to Zoho Books
   * @param {string} variantId - ProductVariant ID
   * @param {string} operation - CREATE or UPDATE
   * @returns {Object} Sync result
   */
  async syncProductVariantItem(variantId, operation = 'CREATE') {
    try {
      const variant = await ProductVariant.findById(variantId);
      if (!variant) {
        throw new Error('Product variant not found');
      }

      const product = await Product.findById(variant.product).populate('category subCategory');
      if (!product) {
        throw new Error('Parent product not found');
      }

      const syncRecord = await zohoBooksService.createSyncRecord({
        entityType: 'ITEM',
        entitySubType: 'PRODUCT_VARIANT',
        localEntityId: variantId,
        localEntityModel: 'ProductVariant',
        operation,
        syncStatus: 'IN_PROGRESS',
        syncData: this.mapProductVariantToItem(variant, product)
      });

      let zohoItem;

      if (operation === 'CREATE') {
        zohoItem = await zohoBooksService.createItem(syncRecord.syncData);
      } else {
        if (!syncRecord.zohoEntityId) {
          throw new Error('Zoho item ID not found for update operation');
        }
        zohoItem = await zohoBooksService.updateItem(syncRecord.zohoEntityId, syncRecord.syncData);
      }

      syncRecord.zohoEntityId = zohoItem.item_id;
      await syncRecord.markSuccess(zohoItem);

      return {
        success: true,
        zohoItem,
        syncRecord
      };

    } catch (error) {
      console.error('Error syncing product variant item:', error);

      const syncRecord = await zohoBooksService.getSyncRecord(variantId, 'ProductVariant', 'ITEM');
      if (syncRecord) {
        await syncRecord.markFailed(error);
      }

      return {
        success: false,
        error: error.message
      };
    }
  }

  // ==================== BULK SYNC OPERATIONS ====================

  /**
   * Sync all temple services as items
   * @returns {Object} Bulk sync result
   */
  async syncAllTempleServices() {
    const results = [];

    // Sync Pooja Schedules
    const poojaSchedules = await PoojaSchedule.find({ deletedAt: null });
    for (const pooja of poojaSchedules) {
      const result = await this.syncPoojaScheduleItem(pooja._id, 'CREATE');
      results.push({
        type: 'POOJA_SCHEDULE',
        id: pooja._id,
        name: pooja.name?.en || pooja.name?.hi,
        ...result
      });
    }

    // Sync Darshan Schedules
    const darshanSchedules = await DarshanSchedule.find({ deletedAt: null });
    for (const darshan of darshanSchedules) {
      const result = await this.syncDarshanScheduleItem(darshan._id, 'CREATE');
      results.push({
        type: 'DARSHAN_SCHEDULE',
        id: darshan._id,
        name: darshan.name?.en || darshan.name?.hi,
        ...result
      });
    }

    // Sync Events
    const events = await Event.find({ deletedAt: null });
    for (const event of events) {
      const result = await this.syncEventItem(event._id, 'CREATE');
      results.push({
        type: 'EVENT',
        id: event._id,
        name: event.name?.en || event.name?.hi,
        ...result
      });
    }

    return {
      total: results.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      results
    };
  }

  /**
   * Sync all products as items
   * @returns {Object} Bulk sync result
   */
  async syncAllProducts() {
    const results = [];

    // Sync Products
    const products = await Product.find({ status: 'APPROVED' });
    for (const product of products) {
      const result = await this.syncProductItem(product._id, 'CREATE');
      results.push({
        type: 'PRODUCT',
        id: product._id,
        name: product.name?.en || product.name?.hi,
        ...result
      });
    }

    // Sync Product Variants
    const variants = await ProductVariant.find({ status: 'ACTIVE' });
    for (const variant of variants) {
      const result = await this.syncProductVariantItem(variant._id, 'CREATE');
      results.push({
        type: 'PRODUCT_VARIANT',
        id: variant._id,
        name: variant.variantName,
        ...result
      });
    }

    return {
      total: results.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      results
    };
  }
}

module.exports = new ZohoItemSyncService();
