const axios = require('axios');
const { zohoConfig } = require('../config/zohoBooks');
const zohoAuth = require('./zohoAuth');
const ZohoSync = require('../models/ZohoSync');

class ZohoBooksService {
  constructor() {
    this.config = zohoConfig;
    this.baseUrl = `${this.config.baseUrl}${this.config.endpoints.contacts}`.replace('/contacts', '');
  }

  /**
   * Make authenticated API request to Zoho Books
   * @param {string} method - HTTP method
   * @param {string} endpoint - API endpoint
   * @param {Object} data - Request data
   * @param {Object} params - Query parameters
   * @returns {Object} API response
   */
  async makeRequest(method, endpoint, data = null, params = {}) {
    try {
      const accessToken = await zohoAuth.getValidAccessToken();
      
      const config = {
        method,
        url: `${this.baseUrl}${endpoint}`,
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        params: {
          organization_id: this.config.organizationId,
          ...params
        }
      };

      if (data && ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
        config.data = data;
      }

      const response = await axios(config);
      return response.data;

    } catch (error) {
      console.error(`Zoho Books API Error [${method} ${endpoint}]:`, error.response?.data || error.message);
      
      if (error.response?.status === 401) {
        throw new Error('Zoho Books authentication failed. Please re-authenticate.');
      }
      
      throw new Error(
        error.response?.data?.message || 
        error.response?.data?.error?.message || 
        error.message || 
        'Zoho Books API request failed'
      );
    }
  }

  // ==================== CONTACTS API ====================

  /**
   * Create a contact in Zoho Books
   * @param {Object} contactData - Contact information
   * @returns {Object} Created contact
   */
  async createContact(contactData) {
    const response = await this.makeRequest('POST', this.config.endpoints.contacts, contactData);
    return response.contact;
  }

  /**
   * Update a contact in Zoho Books
   * @param {string} contactId - Zoho contact ID
   * @param {Object} contactData - Updated contact information
   * @returns {Object} Updated contact
   */
  async updateContact(contactId, contactData) {
    const response = await this.makeRequest('PUT', `${this.config.endpoints.contacts}/${contactId}`, contactData);
    return response.contact;
  }

  /**
   * Get a contact from Zoho Books
   * @param {string} contactId - Zoho contact ID
   * @returns {Object} Contact information
   */
  async getContact(contactId) {
    const response = await this.makeRequest('GET', `${this.config.endpoints.contacts}/${contactId}`);
    return response.contact;
  }

  /**
   * List contacts from Zoho Books
   * @param {Object} filters - Filter parameters
   * @returns {Array} List of contacts
   */
  async listContacts(filters = {}) {
    const response = await this.makeRequest('GET', this.config.endpoints.contacts, null, filters);
    return response.contacts || [];
  }

  // ==================== ITEMS API ====================

  /**
   * Create an item in Zoho Books
   * @param {Object} itemData - Item information
   * @returns {Object} Created item
   */
  async createItem(itemData) {
    const response = await this.makeRequest('POST', this.config.endpoints.items, itemData);
    return response.item;
  }

  /**
   * Update an item in Zoho Books
   * @param {string} itemId - Zoho item ID
   * @param {Object} itemData - Updated item information
   * @returns {Object} Updated item
   */
  async updateItem(itemId, itemData) {
    const response = await this.makeRequest('PUT', `${this.config.endpoints.items}/${itemId}`, itemData);
    return response.item;
  }

  /**
   * Get an item from Zoho Books
   * @param {string} itemId - Zoho item ID
   * @returns {Object} Item information
   */
  async getItem(itemId) {
    const response = await this.makeRequest('GET', `${this.config.endpoints.items}/${itemId}`);
    return response.item;
  }

  /**
   * List items from Zoho Books
   * @param {Object} filters - Filter parameters
   * @returns {Array} List of items
   */
  async listItems(filters = {}) {
    const response = await this.makeRequest('GET', this.config.endpoints.items, null, filters);
    return response.items || [];
  }

  // ==================== INVOICES API ====================

  /**
   * Create an invoice in Zoho Books
   * @param {Object} invoiceData - Invoice information
   * @returns {Object} Created invoice
   */
  async createInvoice(invoiceData) {
    const response = await this.makeRequest('POST', this.config.endpoints.invoices, invoiceData);
    return response.invoice;
  }

  /**
   * Update an invoice in Zoho Books
   * @param {string} invoiceId - Zoho invoice ID
   * @param {Object} invoiceData - Updated invoice information
   * @returns {Object} Updated invoice
   */
  async updateInvoice(invoiceId, invoiceData) {
    const response = await this.makeRequest('PUT', `${this.config.endpoints.invoices}/${invoiceId}`, invoiceData);
    return response.invoice;
  }

  /**
   * Get an invoice from Zoho Books
   * @param {string} invoiceId - Zoho invoice ID
   * @returns {Object} Invoice information
   */
  async getInvoice(invoiceId) {
    const response = await this.makeRequest('GET', `${this.config.endpoints.invoices}/${invoiceId}`);
    return response.invoice;
  }

  /**
   * List invoices from Zoho Books
   * @param {Object} filters - Filter parameters
   * @returns {Array} List of invoices
   */
  async listInvoices(filters = {}) {
    const response = await this.makeRequest('GET', this.config.endpoints.invoices, null, filters);
    return response.invoices || [];
  }

  // ==================== BILLS API ====================

  /**
   * Create a bill in Zoho Books
   * @param {Object} billData - Bill information
   * @returns {Object} Created bill
   */
  async createBill(billData) {
    const response = await this.makeRequest('POST', this.config.endpoints.bills, billData);
    return response.bill;
  }

  /**
   * Update a bill in Zoho Books
   * @param {string} billId - Zoho bill ID
   * @param {Object} billData - Updated bill information
   * @returns {Object} Updated bill
   */
  async updateBill(billId, billData) {
    const response = await this.makeRequest('PUT', `${this.config.endpoints.bills}/${billId}`, billData);
    return response.bill;
  }

  /**
   * Get a bill from Zoho Books
   * @param {string} billId - Zoho bill ID
   * @returns {Object} Bill information
   */
  async getBill(billId) {
    const response = await this.makeRequest('GET', `${this.config.endpoints.bills}/${billId}`);
    return response.bill;
  }

  /**
   * List bills from Zoho Books
   * @param {Object} filters - Filter parameters
   * @returns {Array} List of bills
   */
  async listBills(filters = {}) {
    const response = await this.makeRequest('GET', this.config.endpoints.bills, null, filters);
    return response.bills || [];
  }

  // ==================== ORGANIZATIONS API ====================

  /**
   * Get organization information
   * @returns {Object} Organization details
   */
  async getOrganization() {
    const response = await this.makeRequest('GET', this.config.endpoints.organizations);
    return response.organization;
  }

  // ==================== SYNC TRACKING ====================

  /**
   * Create or update sync record
   * @param {Object} syncData - Sync record data
   * @returns {Object} Sync record
   */
  async createSyncRecord(syncData) {
    const existingSync = await ZohoSync.findOne({
      localEntityId: syncData.localEntityId,
      localEntityModel: syncData.localEntityModel,
      entityType: syncData.entityType
    });

    if (existingSync) {
      Object.assign(existingSync, syncData);
      return await existingSync.save();
    }

    const syncRecord = new ZohoSync(syncData);
    return await syncRecord.save();
  }

  /**
   * Get sync record for entity
   * @param {string} localEntityId - Local entity ID
   * @param {string} localEntityModel - Local entity model name
   * @param {string} entityType - Zoho entity type
   * @returns {Object} Sync record
   */
  async getSyncRecord(localEntityId, localEntityModel, entityType) {
    return await ZohoSync.findOne({
      localEntityId,
      localEntityModel,
      entityType
    });
  }

  /**
   * Get pending sync records
   * @param {Object} filters - Filter criteria
   * @returns {Array} Pending sync records
   */
  async getPendingSyncRecords(filters = {}) {
    const query = {
      syncStatus: { $in: ['PENDING', 'RETRY'] },
      $or: [
        { nextRetryAt: { $exists: false } },
        { nextRetryAt: { $lte: new Date() } }
      ],
      ...filters
    };

    return await ZohoSync.find(query)
      .sort({ createdAt: 1 })
      .limit(50);
  }
}

module.exports = new ZohoBooksService();
