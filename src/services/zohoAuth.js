const axios = require('axios');
const { zohoConfig } = require('../config/zohoBooks');
const ZohoToken = require('../models/ZohoToken');
const { throwBadRequestError, throwNotFoundError } = require('../errors');

class ZohoAuthService {
  constructor() {
    this.config = zohoConfig;
  }

  /**
   * Generate authorization URL for OAuth 2.0 flow
   * @param {string} state - Optional state parameter for security
   * @returns {string} Authorization URL
   */
  generateAuthUrl(state = null) {
    const params = new URLSearchParams({
      response_type: 'code',
      client_id: this.config.clientId,
      scope: this.config.scopes,
      redirect_uri: this.config.redirectUri,
      access_type: 'offline'
    });

    if (state) {
      params.append('state', state);
    }

    return `${this.config.accountsUrl}/oauth/v2/auth?${params.toString()}`;
  }

  /**
   * Exchange authorization code for access token
   * @param {string} code - Authorization code from callback
   * @param {string} adminId - ID of admin who initiated the auth
   * @returns {Object} Token information
   */
  async exchangeCodeForToken(code, adminId) {
    try {
      const tokenData = {
        grant_type: 'authorization_code',
        client_id: this.config.clientId,
        client_secret: this.config.clientSecret,
        redirect_uri: this.config.redirectUri,
        code: code
      };

      const response = await axios.post(
        `${this.config.accountsUrl}/oauth/v2/token`,
        new URLSearchParams(tokenData),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );

      const tokenInfo = response.data;

      // Deactivate existing tokens for this organization
      await ZohoToken.deactivateTokens(this.config.organizationId);

      // Save new token
      const zohoToken = new ZohoToken({
        accessToken: tokenInfo.access_token,
        refreshToken: tokenInfo.refresh_token,
        expiresIn: tokenInfo.expires_in,
        expiresAt: new Date(Date.now() + tokenInfo.expires_in * 1000),
        scope: tokenInfo.scope,
        organizationId: this.config.organizationId,
        createdBy: adminId
      });

      await zohoToken.save();

      return {
        success: true,
        token: zohoToken,
        message: 'Authentication successful'
      };

    } catch (error) {
      console.error('Error exchanging code for token:', error.response?.data || error.message);
      throw new Error(`Authentication failed: ${error.response?.data?.error_description || error.message}`);
    }
  }

  /**
   * Refresh access token using refresh token
   * @param {string} refreshToken - Refresh token
   * @returns {Object} New token information
   */
  async refreshAccessToken(refreshToken) {
    try {
      const tokenData = {
        grant_type: 'refresh_token',
        client_id: this.config.clientId,
        client_secret: this.config.clientSecret,
        refresh_token: refreshToken
      };

      const response = await axios.post(
        `${this.config.accountsUrl}/oauth/v2/token`,
        new URLSearchParams(tokenData),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );

      return response.data;

    } catch (error) {
      console.error('Error refreshing token:', error.response?.data || error.message);
      throw new Error(`Token refresh failed: ${error.response?.data?.error_description || error.message}`);
    }
  }

  /**
   * Get valid access token (refresh if needed)
   * @returns {string} Valid access token
   */
  async getValidAccessToken() {
    const token = await ZohoToken.getActiveToken(this.config.organizationId);
    
    if (!token) {
      throw throwNotFoundError('No active Zoho Books token found. Please authenticate first.');
    }

    // Check if token needs refresh
    if (token.needsRefresh) {
      try {
        const newTokenData = await this.refreshAccessToken(token.refreshToken);
        await token.updateToken(newTokenData);
        await token.recordUsage();
        return token.accessToken;
      } catch (error) {
        await token.recordError(error);
        throw throwBadRequestError('Failed to refresh Zoho Books token. Please re-authenticate.');
      }
    }

    await token.recordUsage();
    return token.accessToken;
  }

  /**
   * Revoke access token
   * @param {string} accessToken - Access token to revoke
   * @returns {boolean} Success status
   */
  async revokeToken(accessToken) {
    try {
      await axios.post(
        `${this.config.accountsUrl}/oauth/v2/token/revoke`,
        new URLSearchParams({ token: accessToken }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );

      // Deactivate tokens in database
      await ZohoToken.deactivateTokens(this.config.organizationId);

      return true;

    } catch (error) {
      console.error('Error revoking token:', error.response?.data || error.message);
      return false;
    }
  }

  /**
   * Check if authentication is valid
   * @returns {boolean} Authentication status
   */
  async isAuthenticated() {
    try {
      const token = await ZohoToken.getActiveToken(this.config.organizationId);
      return token && !token.isExpired;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get authentication status and token info
   * @returns {Object} Authentication status and details
   */
  async getAuthStatus() {
    const token = await ZohoToken.getActiveToken(this.config.organizationId);
    
    if (!token) {
      return {
        isAuthenticated: false,
        message: 'No authentication token found'
      };
    }

    return {
      isAuthenticated: !token.isExpired,
      expiresAt: token.expiresAt,
      needsRefresh: token.needsRefresh,
      lastUsed: token.lastUsed,
      usageCount: token.usageCount,
      scope: token.scope
    };
  }
}

module.exports = new ZohoAuthService();
