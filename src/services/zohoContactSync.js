const zohoBooksService = require('./zohoBooks');
const Temple = require('../models/Temple');
const Vendor = require('../models/Vendor');
const User = require('../models/User');

class ZohoContactSyncService {
  
  // ==================== TEMPLE VENDOR SYNC ====================

  /**
   * Map temple data to Zoho contact format
   * @param {Object} temple - Temple document
   * @returns {Object} Zoho contact data
   */
  mapTempleToContact(temple) {
    return {
      contact_name: temple.name?.en || temple.name?.hi || 'Temple',
      contact_type: 'vendor',
      vendor_name: temple.name?.en || temple.name?.hi || 'Temple',
      contact_persons: [{
        first_name: 'Temple',
        last_name: 'Admin',
        email: temple.email || '',
        phone: temple.phoneNumber || '',
        is_primary_contact: true
      }],
      billing_address: {
        address: temple.city?.en || temple.city?.hi || '',
        city: temple.city?.en || temple.city?.hi || '',
        state: temple.state?.en || temple.state?.hi || '',
        country: 'India'
      },
      notes: `Temple: ${temple.name?.en || temple.name?.hi || ''}\nDeity: ${temple.deity?.en || temple.deity?.hi || ''}\nLocation: ${temple.locationUrl || ''}`,
      custom_fields: [
        {
          customfield_id: 'temple_id',
          value: temple._id.toString()
        },
        {
          customfield_id: 'vendor_type',
          value: 'TEMPLE'
        }
      ]
    };
  }

  /**
   * Sync temple as contact to Zoho Books
   * @param {string} templeId - Temple ID
   * @param {string} operation - CREATE or UPDATE
   * @returns {Object} Sync result
   */
  async syncTempleContact(templeId, operation = 'CREATE') {
    try {
      const temple = await Temple.findById(templeId);
      if (!temple) {
        throw new Error('Temple not found');
      }

      // Create sync record
      const syncRecord = await zohoBooksService.createSyncRecord({
        entityType: 'CONTACT',
        entitySubType: 'TEMPLE_VENDOR',
        localEntityId: templeId,
        localEntityModel: 'Temple',
        operation,
        syncStatus: 'IN_PROGRESS',
        syncData: this.mapTempleToContact(temple)
      });

      let zohoContact;
      
      if (operation === 'CREATE') {
        zohoContact = await zohoBooksService.createContact(syncRecord.syncData);
      } else {
        // For update, we need the Zoho contact ID
        if (!syncRecord.zohoEntityId) {
          throw new Error('Zoho contact ID not found for update operation');
        }
        zohoContact = await zohoBooksService.updateContact(syncRecord.zohoEntityId, syncRecord.syncData);
      }

      // Update sync record with success
      syncRecord.zohoEntityId = zohoContact.contact_id;
      await syncRecord.markSuccess(zohoContact);

      return {
        success: true,
        zohoContact,
        syncRecord
      };

    } catch (error) {
      console.error('Error syncing temple contact:', error);
      
      // Update sync record with failure
      const syncRecord = await zohoBooksService.getSyncRecord(templeId, 'Temple', 'CONTACT');
      if (syncRecord) {
        await syncRecord.markFailed(error);
      }

      return {
        success: false,
        error: error.message
      };
    }
  }

  // ==================== PRODUCT VENDOR SYNC ====================

  /**
   * Map vendor data to Zoho contact format
   * @param {Object} vendor - Vendor document
   * @returns {Object} Zoho contact data
   */
  mapVendorToContact(vendor) {
    return {
      contact_name: vendor.businessName?.en || vendor.businessName?.hi || vendor.name?.en || vendor.name?.hi || 'Vendor',
      contact_type: 'vendor',
      vendor_name: vendor.businessName?.en || vendor.businessName?.hi || vendor.name?.en || vendor.name?.hi || 'Vendor',
      contact_persons: [{
        first_name: vendor.contactPersonName || vendor.name?.en || 'Contact',
        last_name: 'Person',
        email: vendor.email || '',
        phone: vendor.phoneNumber || '',
        is_primary_contact: true
      }],
      billing_address: {
        address: vendor.address?.fullAddress || '',
        zip: vendor.address?.pincode || '',
        country: 'India'
      },
      gst_no: vendor.gstNumber || '',
      pan_no: vendor.panNumber || '',
      notes: `Business: ${vendor.businessName?.en || vendor.businessName?.hi || ''}\nContact: ${vendor.contactPersonName || ''}\nGST: ${vendor.gstNumber || ''}`,
      custom_fields: [
        {
          customfield_id: 'vendor_id',
          value: vendor._id.toString()
        },
        {
          customfield_id: 'vendor_type',
          value: 'PRODUCT_VENDOR'
        }
      ]
    };
  }

  /**
   * Sync vendor as contact to Zoho Books
   * @param {string} vendorId - Vendor ID
   * @param {string} operation - CREATE or UPDATE
   * @returns {Object} Sync result
   */
  async syncVendorContact(vendorId, operation = 'CREATE') {
    try {
      const vendor = await Vendor.findById(vendorId);
      if (!vendor) {
        throw new Error('Vendor not found');
      }

      // Create sync record
      const syncRecord = await zohoBooksService.createSyncRecord({
        entityType: 'CONTACT',
        entitySubType: 'PRODUCT_VENDOR',
        localEntityId: vendorId,
        localEntityModel: 'Vendor',
        operation,
        syncStatus: 'IN_PROGRESS',
        syncData: this.mapVendorToContact(vendor)
      });

      let zohoContact;
      
      if (operation === 'CREATE') {
        zohoContact = await zohoBooksService.createContact(syncRecord.syncData);
      } else {
        if (!syncRecord.zohoEntityId) {
          throw new Error('Zoho contact ID not found for update operation');
        }
        zohoContact = await zohoBooksService.updateContact(syncRecord.zohoEntityId, syncRecord.syncData);
      }

      // Update sync record with success
      syncRecord.zohoEntityId = zohoContact.contact_id;
      await syncRecord.markSuccess(zohoContact);

      return {
        success: true,
        zohoContact,
        syncRecord
      };

    } catch (error) {
      console.error('Error syncing vendor contact:', error);
      
      const syncRecord = await zohoBooksService.getSyncRecord(vendorId, 'Vendor', 'CONTACT');
      if (syncRecord) {
        await syncRecord.markFailed(error);
      }

      return {
        success: false,
        error: error.message
      };
    }
  }

  // ==================== CUSTOMER SYNC ====================

  /**
   * Map user data to Zoho customer contact format
   * @param {Object} user - User document
   * @returns {Object} Zoho contact data
   */
  mapUserToContact(user) {
    return {
      contact_name: `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'Customer',
      contact_type: 'customer',
      contact_persons: [{
        first_name: user.firstName || 'Customer',
        last_name: user.lastName || '',
        email: user.email || '',
        phone: user.phoneNumber || '',
        is_primary_contact: true
      }],
      notes: `Customer ID: ${user._id}\nPhone: ${user.countryCode || '+91'} ${user.phoneNumber || ''}`,
      custom_fields: [
        {
          customfield_id: 'user_id',
          value: user._id.toString()
        },
        {
          customfield_id: 'contact_type',
          value: 'CUSTOMER'
        }
      ]
    };
  }

  /**
   * Sync user as customer contact to Zoho Books
   * @param {string} userId - User ID
   * @param {string} operation - CREATE or UPDATE
   * @returns {Object} Sync result
   */
  async syncUserContact(userId, operation = 'CREATE') {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      const syncRecord = await zohoBooksService.createSyncRecord({
        entityType: 'CONTACT',
        entitySubType: 'CUSTOMER',
        localEntityId: userId,
        localEntityModel: 'User',
        operation,
        syncStatus: 'IN_PROGRESS',
        syncData: this.mapUserToContact(user)
      });

      let zohoContact;
      
      if (operation === 'CREATE') {
        zohoContact = await zohoBooksService.createContact(syncRecord.syncData);
      } else {
        if (!syncRecord.zohoEntityId) {
          throw new Error('Zoho contact ID not found for update operation');
        }
        zohoContact = await zohoBooksService.updateContact(syncRecord.zohoEntityId, syncRecord.syncData);
      }

      syncRecord.zohoEntityId = zohoContact.contact_id;
      await syncRecord.markSuccess(zohoContact);

      return {
        success: true,
        zohoContact,
        syncRecord
      };

    } catch (error) {
      console.error('Error syncing user contact:', error);
      
      const syncRecord = await zohoBooksService.getSyncRecord(userId, 'User', 'CONTACT');
      if (syncRecord) {
        await syncRecord.markFailed(error);
      }

      return {
        success: false,
        error: error.message
      };
    }
  }

  // ==================== BULK SYNC OPERATIONS ====================

  /**
   * Sync all temples as contacts
   * @returns {Object} Bulk sync result
   */
  async syncAllTemples() {
    const temples = await Temple.find({ deletedAt: null });
    const results = [];

    for (const temple of temples) {
      const result = await this.syncTempleContact(temple._id, 'CREATE');
      results.push({
        templeId: temple._id,
        templeName: temple.name?.en || temple.name?.hi,
        ...result
      });
    }

    return {
      total: temples.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      results
    };
  }

  /**
   * Sync all vendors as contacts
   * @returns {Object} Bulk sync result
   */
  async syncAllVendors() {
    const vendors = await Vendor.find();
    const results = [];

    for (const vendor of vendors) {
      const result = await this.syncVendorContact(vendor._id, 'CREATE');
      results.push({
        vendorId: vendor._id,
        vendorName: vendor.businessName?.en || vendor.name?.en,
        ...result
      });
    }

    return {
      total: vendors.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      results
    };
  }
}

module.exports = new ZohoContactSyncService();
