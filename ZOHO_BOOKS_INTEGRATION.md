# Zoho Books Integration

This document describes the Zoho Books integration implementation for the One God Node.js application.

## Overview

The integration synchronizes data from the backend server to Zoho Books (one-way sync) and includes:

- **Two types of Vendors**: Temples and Product Vendors
- **Two types of Items**: Temple Services and E-Shop Products
- **Contacts Management**: Sync vendors and customers
- **Items Management**: Sync services and products
- **Invoices Management**: Generate invoices for bookings and orders
- **Bills Management**: Handle vendor settlements

## Prerequisites

1. **Install Dependencies**
   ```bash
   npm install zohobooksapi
   ```

2. **Environment Variables**
   Add the following to your `.env` file:
   ```env
   # Zoho Books Configuration
   ZOHO_CLIENT_ID=your_client_id
   ZOHO_CLIENT_SECRET=your_client_secret
   ZOHO_ORGANIZATION_ID=your_organization_id
   ZOHO_REDIRECT_URI=https://49c43afed0d6.ngrok-free.app/zoho/callback
   ZOHO_BASE_URL=https://www.zohoapis.com
   ZOHO_ACCOUNTS_URL=https://accounts.zoho.com
   ```

## Architecture

### Models

1. **ZohoSync** - Tracks sync operations and status
2. **ZohoToken** - Stores OAuth tokens and manages authentication

### Services

1. **zohoAuth** - Handles OAuth 2.0 authentication
2. **zohoBooksService** - Core API wrapper for Zoho Books
3. **zohoContactSync** - Syncs vendors and customers as contacts
4. **zohoItemSync** - Syncs temple services and products as items
5. **zohoInvoiceSync** - Generates invoices for bookings and orders

### API Endpoints

#### Authentication
- `GET /api/v1/admin/zoho-books/auth/url` - Get authorization URL
- `GET /zoho/callback` - OAuth callback (public)
- `GET /api/v1/admin/zoho-books/auth/status` - Check auth status
- `DELETE /api/v1/admin/zoho-books/auth/revoke` - Revoke authentication

#### Sync Operations
- `POST /api/v1/admin/zoho-books/sync/contacts` - Sync all contacts
- `POST /api/v1/admin/zoho-books/sync/items` - Sync all items
- `POST /api/v1/admin/zoho-books/sync/invoices` - Sync all invoices
- `POST /api/v1/admin/zoho-books/sync/entity` - Sync specific entity

#### Monitoring
- `GET /api/v1/admin/zoho-books/sync/status` - Get sync status
- `GET /api/v1/admin/zoho-books/sync/failed` - Get failed syncs
- `POST /api/v1/admin/zoho-books/sync/retry` - Retry failed syncs

## Setup Instructions

### 1. Zoho Books App Configuration

1. Go to [Zoho Developer Console](https://api-console.zoho.com/)
2. Create a new application
3. Set the redirect URI to: `https://49c43afed0d6.ngrok-free.app/zoho/callback`
4. Note down the Client ID and Client Secret

### 2. Authentication Flow

1. Admin calls `/api/v1/admin/zoho-books/auth/url` to get authorization URL
2. Admin visits the URL and grants permissions
3. Zoho redirects to `/zoho/callback` with authorization code
4. System exchanges code for access token and stores it

### 3. Data Sync

#### Contacts Sync
- **Temples**: Synced as vendor contacts with temple information
- **Product Vendors**: Synced as vendor contacts with business details
- **Users**: Synced as customer contacts for invoicing

#### Items Sync
- **Temple Services**: PoojaSchedule, DarshanSchedule, Events as service items
- **Products**: E-shop products and variants as inventory items

#### Invoice Generation
- **Bookings**: Generate invoices for temple service bookings
- **Orders**: Generate invoices for e-shop orders

## Usage Examples

### 1. Initial Setup
```javascript
// 1. Get authorization URL
const authResponse = await fetch('/api/v1/admin/zoho-books/auth/url');
const { authUrl } = await authResponse.json();

// 2. Visit authUrl in browser and complete OAuth flow

// 3. Check authentication status
const statusResponse = await fetch('/api/v1/admin/zoho-books/auth/status');
const status = await statusResponse.json();
```

### 2. Bulk Sync Operations
```javascript
// Sync all contacts
await fetch('/api/v1/admin/zoho-books/sync/contacts', { method: 'POST' });

// Sync all items
await fetch('/api/v1/admin/zoho-books/sync/items', { method: 'POST' });

// Sync all invoices
await fetch('/api/v1/admin/zoho-books/sync/invoices', { method: 'POST' });
```

### 3. Individual Entity Sync
```javascript
// Sync specific temple as contact
await fetch('/api/v1/admin/zoho-books/sync/entity', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    entityType: 'TEMPLE_CONTACT',
    entityId: 'temple_id_here',
    operation: 'CREATE'
  })
});
```

### 4. Monitor Sync Status
```javascript
// Get sync status overview
const statusResponse = await fetch('/api/v1/admin/zoho-books/sync/status');

// Get failed syncs
const failedResponse = await fetch('/api/v1/admin/zoho-books/sync/failed');

// Retry failed syncs
await fetch('/api/v1/admin/zoho-books/sync/retry', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ syncIds: ['sync_id_1', 'sync_id_2'] })
});
```

## Data Mapping

### Temple to Contact
```javascript
{
  contact_name: "Temple Name",
  contact_type: "vendor",
  vendor_name: "Temple Name",
  billing_address: { city: "City", state: "State", country: "India" },
  custom_fields: [
    { customfield_id: "temple_id", value: "temple_id" },
    { customfield_id: "vendor_type", value: "TEMPLE" }
  ]
}
```

### Product Vendor to Contact
```javascript
{
  contact_name: "Business Name",
  contact_type: "vendor",
  vendor_name: "Business Name",
  gst_no: "GST Number",
  pan_no: "PAN Number",
  custom_fields: [
    { customfield_id: "vendor_id", value: "vendor_id" },
    { customfield_id: "vendor_type", value: "PRODUCT_VENDOR" }
  ]
}
```

### Temple Service to Item
```javascript
{
  name: "Service Name",
  item_type: "service",
  category_name: "Temple Services",
  rate: "Base Price",
  tax_percentage: "GST Percentage",
  custom_fields: [
    { customfield_id: "service_id", value: "service_id" },
    { customfield_id: "service_type", value: "POOJA_SCHEDULE" }
  ]
}
```

## Error Handling

The system includes comprehensive error handling:

1. **Retry Logic**: Failed syncs are automatically retried with exponential backoff
2. **Error Tracking**: All errors are logged with detailed information
3. **Status Monitoring**: Real-time sync status tracking
4. **Manual Retry**: Admins can manually retry failed operations

## Security

1. **OAuth 2.0**: Secure authentication with Zoho Books
2. **Token Management**: Automatic token refresh and secure storage
3. **Role-based Access**: Only Admin/SuperAdmin can access sync operations
4. **State Parameter**: CSRF protection in OAuth flow

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Check client ID and secret
   - Verify redirect URI matches exactly
   - Ensure organization ID is correct

2. **Sync Failures**
   - Check network connectivity
   - Verify Zoho Books API limits
   - Review error logs in sync records

3. **Token Expired**
   - System automatically refreshes tokens
   - If refresh fails, re-authenticate

### Logs and Monitoring

- Check sync status via API endpoints
- Review ZohoSync collection for detailed error information
- Monitor token usage and expiry

## Rate Limits

Zoho Books API has rate limits:
- 100 requests per minute
- 1000 requests per day

The system includes rate limiting protection and retry mechanisms.
